ПЛАН РАЗРАБОТКИ МОДУЛЯ SUMM.PY
=====================================

ЦЕЛЬ: Добавить в бота новый модуль summ.py для автоматического сокращения длинных текстов в группах

ТРЕБОВАНИЯ:
- Работает только в ГРУППАХ (не в приватных чатах)
- Активируется при получении текста длиннее 600 символов
- Использует qwen-3-235b-a22b через Cerebras API
- Во время сокращения ничего не пишет в чат
- После сокращения отправляет результат
- Удаляет теги </think> из ответа ИИ
- В системном промпте просит не использовать форматирование
- Авторотация API ключей Cerebras

АНАЛИЗ СТРУКТУРЫ ПРОЕКТА:
- main.py: точка входа, импортирует handlers
- handlers.py: основные обработчики, строка 5864 - определение group_chat, строка 5866 - блок обработки групп
- config.py: конфигурация, строка 14 - существующий ключ Cerebras
- cerebras_client.py: клиент для Cerebras API, нужно расширить

═══════════════════════════════════════════════════════════════════════════════

ЭТАП 1: РАСШИРЕНИЕ CEREBRAS_CLIENT.PY
=====================================

ЗАДАЧИ:
1. Добавить новые API ключи Cerebras в список
2. Реализовать авторотацию ключей при ошибках
3. Добавить функцию для сокращения текста через qwen-3-235b-a22b
4. Добавить обработку тегов </think>
5. Создать системный промпт для сокращения

ФАЙЛЫ ДЛЯ ИЗМЕНЕНИЯ:
- cerebras_client.py

ДЕТАЛИ РЕАЛИЗАЦИИ:
- Создать список CEREBRAS_API_KEYS с новыми ключами
- Добавить индекс текущего ключа и логику переключения
- Создать функцию summarize_text() с моделью qwen-3-235b-a22b
- Системный промпт: просить сократить текст, выделить ключевые моменты, не использовать форматирование
- Обработка ответа: удаление тегов </think>

МЕСТО ДЛЯ ЗАПИСИ ИЗМЕНЕНИЙ:
_____________________________________________________________________________
|                                                                           |
| ЧТО ИЗМЕНЕНО:                                                            |
|                                                                           |
|                                                                           |
|                                                                           |
| ИНФОРМАЦИЯ ДЛЯ СЛЕДУЮЩЕГО ЭТАПА:                                         |
|                                                                           |
|                                                                           |
|___________________________________________________________________________|

ТЕСТЫ ЭТАПА 1:
- Проверить работу авторотации ключей
- Протестировать функцию summarize_text() с длинным текстом
- Убедиться, что теги </think> удаляются

═══════════════════════════════════════════════════════════════════════════════

ЭТАП 2: СОЗДАНИЕ МОДУЛЯ SUMM.PY
===============================

ЗАДАЧИ:
1. Создать новый файл summ.py
2. Импортировать необходимые зависимости
3. Создать функцию check_and_summarize_text()
4. Добавить проверку длины текста (>600 символов)
5. Интегрировать с cerebras_client

ФАЙЛЫ ДЛЯ СОЗДАНИЯ:
- summ.py

ДЕТАЛИ РЕАЛИЗАЦИИ:
- Функция check_and_summarize_text(message, bot)
- Проверка: message.chat.type in ["group", "supergroup"]
- Проверка: message.content_type == "text" and len(message.text) > 600
- Вызов cerebras_client.summarize_text()
- Отправка результата в чат через bot.reply_to()

СТРУКТУРА МОДУЛЯ:
```python
import telebot
from cerebras_client import summarize_text
from bot_globals import log_admin

def check_and_summarize_text(message, bot):
    """Проверяет и сокращает длинный текст в группах"""
    # Логика проверки и сокращения
    pass
```

МЕСТО ДЛЯ ЗАПИСИ ИЗМЕНЕНИЙ:
_____________________________________________________________________________
|                                                                           |
| ЧТО СОЗДАНО:                                                             |
|                                                                           |
|                                                                           |
|                                                                           |
| ИНФОРМАЦИЯ ДЛЯ СЛЕДУЮЩЕГО ЭТАПА:                                         |
|                                                                           |
|                                                                           |
|___________________________________________________________________________|

ТЕСТЫ ЭТАПА 2:
- Проверить импорты модуля
- Протестировать функцию с коротким текстом (не должна срабатывать)
- Протестировать функцию с длинным текстом в группе
- Проверить работу в приватном чате (не должна срабатывать)

═══════════════════════════════════════════════════════════════════════════════

ЭТАП 3: ИНТЕГРАЦИЯ В HANDLERS.PY
================================

ЗАДАЧИ:
1. Импортировать модуль summ в handlers.py
2. Найти блок обработки групп (строка 5866)
3. Добавить вызов check_and_summarize_text() для текстовых сообщений
4. Разместить проверку до блока "Regular messages in groups are ignored"

ФАЙЛЫ ДЛЯ ИЗМЕНЕНИЯ:
- handlers.py

МЕСТО ИНТЕГРАЦИИ:
- Строка ~5866: if group_chat:
- Добавить проверку текстовых сообщений перед строкой 6041

ДЕТАЛИ РЕАЛИЗАЦИИ:
```python
# В начале файла добавить импорт
import summ

# В блоке if group_chat: добавить
if message.content_type == "text" and message.text:
    # Проверить и сократить длинный текст
    summ.check_and_summarize_text(message, bot)
```

МЕСТО ДЛЯ ЗАПИСИ ИЗМЕНЕНИЙ:
_____________________________________________________________________________
|                                                                           |
| ЧТО ИЗМЕНЕНО В HANDLERS.PY:                                              |
|                                                                           |
|                                                                           |
|                                                                           |
| НОМЕРА СТРОК ИЗМЕНЕНИЙ:                                                  |
|                                                                           |
|                                                                           |
|___________________________________________________________________________|

ТЕСТЫ ЭТАПА 3:
- Отправить короткий текст в группу (не должен сокращаться)
- Отправить длинный текст в группу (должен сокращаться)
- Отправить длинный текст в приватный чат (не должен сокращаться)
- Проверить, что остальная функциональность бота не нарушена

═══════════════════════════════════════════════════════════════════════════════

ЭТАП 4: ФИНАЛЬНОЕ ТЕСТИРОВАНИЕ И ОТЛАДКА
========================================

ЗАДАЧИ:
1. Комплексное тестирование всех сценариев
2. Проверка производительности
3. Тестирование авторотации ключей
4. Проверка обработки ошибок

СЦЕНАРИИ ТЕСТИРОВАНИЯ:
1. Короткий текст в группе (< 600 символов) - не должен обрабатываться
2. Длинный текст в группе (> 600 символов) - должен сокращаться
3. Длинный текст в приватном чате - не должен обрабатываться
4. Ошибка API - должна переключиться на следующий ключ
5. Все ключи недоступны - должна логироваться ошибка
6. Текст с тегами </think> - теги должны удаляться

ПРОВЕРКИ КАЧЕСТВА:
- Время ответа модуля
- Качество сокращения текста
- Отсутствие форматирования в ответе
- Корректность логирования

МЕСТО ДЛЯ ЗАПИСИ РЕЗУЛЬТАТОВ:
_____________________________________________________________________________
|                                                                           |
| РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:                                                 |
|                                                                           |
| Сценарий 1 (короткий текст в группе):                                   |
|                                                                           |
| Сценарий 2 (длинный текст в группе):                                    |
|                                                                           |
| Сценарий 3 (длинный текст в приватном чате):                            |
|                                                                           |
| Сценарий 4 (ошибка API):                                                |
|                                                                           |
| Сценарий 5 (все ключи недоступны):                                      |
|                                                                           |
| Сценарий 6 (теги </think>):                                             |
|                                                                           |
| НАЙДЕННЫЕ ПРОБЛЕМЫ И ИСПРАВЛЕНИЯ:                                       |
|                                                                           |
|                                                                           |
|___________________________________________________________________________|

═══════════════════════════════════════════════════════════════════════════════

ДОПОЛНИТЕЛЬНЫЕ КЛЮЧИ CEREBRAS ДЛЯ ДОБАВЛЕНИЯ:
csk-2rt6htct4nvwmpd6234kyym23d6pc6h5jfh533ykc38n6jed (уже есть)
csk-xjcxn3c93d8xj9jj5pxf6wxt4hx4v38rj4852pfkefmn6f5c
csk-y8n8djymknet3tehpr5r26cjt9x5ev68yetpndkjcr939hyp
csk-xetknxkp2yj3y52w9vj3drdrrxrkvrdfh6hnvwpyev6jynek

ВАЖНЫЕ ЗАМЕЧАНИЯ:
- НЕ ВЫПОЛНЯТЬ ПЛАН! Только создать его
- После каждого этапа обязательно тестировать
- Записывать все изменения в соответствующие места
- Модуль должен работать бесшумно (не мешать основной функциональности)
- Обязательно логировать все действия модуля

ПЛАН ГОТОВ К ВЫПОЛНЕНИЮ ✓
